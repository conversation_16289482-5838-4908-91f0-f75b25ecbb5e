import bot from "./bot";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Rate limiting utility
async function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      if (error.response?.error_code === 429) {
        const retryAfter =
          error.response.parameters?.retry_after ?? baseDelay / 1000;
        console.log(
          `⏳ Rate limited. Waiting ${retryAfter} seconds before retry ${attempt}/${maxRetries}...`
        );

        if (attempt === maxRetries) {
          throw error;
        }

        await delay(retryAfter * 1000);
      } else {
        throw error;
      }
    }
  }
  throw new Error("Max retries exceeded");
}

const PORT = process.env.PORT ?? 3001;
const NODE_ENV = process.env.NODE_ENV ?? "development";
const WEBHOOK_URL = process.env.WEBHOOK_URL;

async function startBot() {
  try {
    console.log("🤖 Starting Marketplace Bot...");

    const botInfo = await retryWithBackoff(() => bot.telegram.getMe());
    console.log(`✅ Bot @${botInfo.username} is ready`);
    console.log(
      `🔍 Bot ID: ${botInfo.id}, Can join groups: ${botInfo.can_join_groups}, Can read messages: ${botInfo.can_read_all_group_messages}`
    );

    await delay(500);

    if (NODE_ENV === "production" && WEBHOOK_URL) {
      console.log("🌐 Setting up webhook for production...");
      await retryWithBackoff(() =>
        bot.telegram.setWebhook(`${WEBHOOK_URL}/webhook`)
      );

      // Start webhook server
      bot.webhookCallback("/webhook");
      console.log(`🚀 Bot webhook server started on port ${PORT}`);
    } else {
      console.log("🔄 Starting bot in polling mode (development)...");

      try {
        await retryWithBackoff(() =>
          bot.telegram.deleteWebhook({ drop_pending_updates: true })
        );
        console.log("✅ Webhook removed and pending updates dropped");
      } catch (error) {
        console.log(
          "ℹ️ No webhook to remove or error removing webhook:",
          error instanceof Error ? error.message : String(error)
        );
      }

      await delay(1000);

      console.log("🔄 Attempting to launch bot...");
      try {
        // Try launching without dropPendingUpdates first
        console.log("🔄 Launching bot without dropPendingUpdates...");
        await bot.launch();
        console.log("🚀 Bot started successfully in polling mode");
      } catch (error: any) {
        console.error("❌ Failed to launch bot:", error);
        if (
          error.message?.includes("409") ||
          error.message?.includes("Conflict")
        ) {
          console.log(
            "💡 Tip: Another bot instance might be running. Please stop it first."
          );
          console.log("💡 You can also wait a few seconds and try again.");
        }
        throw error;
      }
    }

    await delay(1000);

    console.log("🔧 Setting up global menu button...");
    try {
      await retryWithBackoff(() =>
        bot.telegram.setChatMenuButton({
          menuButton: {
            type: "web_app",
            text: "Open Marketplace",
            web_app: {
              url:
                process.env.WEB_APP_URL ??
                "https://4d5rqhd0-3000.euw.devtunnels.ms/",
            },
          },
        })
      );
      console.log("✅ Global menu button configured");
    } catch (error) {
      console.log("⚠️ Failed to set menu button:", error);
    }

    await delay(1000);

    console.log("🔧 Setting up bot commands...");
    try {
      await retryWithBackoff(() =>
        bot.telegram.setMyCommands([
          { command: "start", description: "Start the bot and show main menu" },
          { command: "help", description: "Show help information" },
        ])
      );
      console.log("✅ Bot commands configured");
    } catch (error) {
      console.log("⚠️ Failed to set commands:", error);
    }

    await delay(1000);

    console.log("🔧 Setting up bot description...");
    try {
      await retryWithBackoff(() =>
        bot.telegram.setMyDescription(
          "🛍️ Marketplace Bot - Your gateway to the PREM marketplace platform. Use the menu button or commands to get started!"
        )
      );

      await delay(500);

      await retryWithBackoff(() =>
        bot.telegram.setMyShortDescription("🛍️ Access the marketplace platform")
      );
      console.log("✅ Bot description configured");
    } catch (error) {
      console.log("⚠️ Failed to set description:", error);
    }

    console.log("🎉 Bot setup completed successfully!");
  } catch (error) {
    console.error("❌ Failed to start bot:", error);
    process.exit(1);
  }
}

process.once("SIGINT", () => {
  console.log("🛑 Received SIGINT, shutting down gracefully...");
  bot.stop("SIGINT");
  process.exit(0);
});

process.once("SIGTERM", () => {
  console.log("🛑 Received SIGTERM, shutting down gracefully...");
  bot.stop("SIGTERM");
  process.exit(0);
});

process.on("uncaughtException", (error) => {
  console.error("💥 Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("💥 Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

startBot();
